import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_hub_service.dart';
import '../services/app_state.dart';

class RepositoryEditor extends StatefulWidget {
  final AppHubService appHubService;
  final File? initialFile;

  const RepositoryEditor({
    super.key,
    required this.appHubService,
    this.initialFile,
  });

  @override
  State<RepositoryEditor> createState() => _RepositoryEditorState();
}

class _RepositoryEditorState extends State<RepositoryEditor>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _rawJsonController = TextEditingController();
  bool _isLoading = true;
  bool _hasChanges = false;
  bool _isSaving = false;
  String? _error;
  String _originalContent = '';
  int _lineCount = 0;
  int _characterCount = 0;

  // GUI Editor controllers
  final _nameController = TextEditingController();
  final _versionController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _sourceController = TextEditingController();
  final _lastUpdatedController = TextEditingController();
  List<String> _selectedRepositoryTypes = ['apt'];
  String _currentFileName = 'repository.json';
  String? _currentFilePath; // Track the full path of the current file

  AppHubRepository? _repository;
  List<AppCategory> _categories = [];
  List<AppHubApplication> _applications = [];
  List<AppHubApplication> _filteredApplications = [];
  final _applicationSearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _rawJsonController.addListener(_onRawJsonChanged);

    if (widget.initialFile != null) {
      _loadSpecificRepositoryFile(widget.initialFile!);
    } else {
      // Set loading to false since we're showing repository manager
      setState(() {
        _isLoading = false;
      });
      // Show repository manager first
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showRepositoryManager();
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _rawJsonController.dispose();
    _nameController.dispose();
    _versionController.dispose();
    _descriptionController.dispose();
    _sourceController.dispose();
    _lastUpdatedController.dispose();
    _applicationSearchController.dispose();
    super.dispose();
  }

  void _onRawJsonChanged() {
    final text = _rawJsonController.text;
    final newLineCount = text.split('\n').length;
    final newCharacterCount = text.length;
    final newHasChanges = text != _originalContent;

    if (newLineCount != _lineCount ||
        newCharacterCount != _characterCount ||
        newHasChanges != _hasChanges) {
      setState(() {
        _lineCount = newLineCount;
        _characterCount = newCharacterCount;
        _hasChanges = newHasChanges;
      });
    }
  }

  void _onTabChanged() {
    // When switching to GUI tab, sync from JSON
    if (_tabController.index == 0) {
      _syncGuiFromJson();
    }
  }

  void _syncGuiFromJson() {
    try {
      final jsonData =
          jsonDecode(_rawJsonController.text) as Map<String, dynamic>;
      final repository = AppHubRepository.fromJson(jsonData);

      setState(() {
        _repository = repository;
        _nameController.text = repository.repository.name;
        _versionController.text = repository.repository.version;
        _descriptionController.text = repository.repository.description;
        _sourceController.text = repository.repository.source;
        _lastUpdatedController.text =
            repository.repository.lastUpdated.toIso8601String().split('T')[0];
        _selectedRepositoryTypes = List.from(repository.repository.type);

        _categories = List.from(repository.categories);
        _categories.sort((a, b) => a.name.compareTo(b.name));

        _applications = List.from(repository.applications);
        _applications.sort((a, b) => a.name.compareTo(b.name));
        _filterApplications();
      });
    } catch (e) {
      // Invalid JSON, don't update GUI
    }
  }

  Future<void> _saveRepository() async {
    setState(() {
      _isSaving = true;
    });

    try {
      String contentToSave;

      // If we're on the GUI tab, build JSON from GUI data
      if (_tabController.index == 0) {
        contentToSave = _buildJsonFromGui();
      } else {
        // Use raw JSON content
        contentToSave = _rawJsonController.text;
      }

      // Validate JSON
      jsonDecode(contentToSave);

      // Save to file
      if (_currentFilePath == null) {
        throw Exception(
            'No repository file loaded. Please select a repository from the manager first.');
      }

      final repositoryFile = File(_currentFilePath!);
      await repositoryFile.writeAsString(contentToSave);

      // Reload repository in service
      await widget.appHubService.loadRepository();

      setState(() {
        _originalContent = contentToSave;
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Repository saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  String _buildJsonFromGui() {
    final repositoryData = {
      'repository': {
        'name': _nameController.text,
        'version': _versionController.text,
        'description': _descriptionController.text,
        'source': _sourceController.text,
        'type': _selectedRepositoryTypes,
        'last_updated': DateTime.now().toIso8601String(),
      },
      'categories': _categories
          .map((cat) => {
                'id': cat.id,
                'name': cat.name,
                'description': cat.description,
                'icon': cat.icon,
              })
          .toList(),
      'applications': _applications
          .map((app) => {
                'id': app.id,
                'name': app.name,
                'description': app.description,
                'version': app.version,
                'category': app.category,
                'icon': app.icon,
                'size': app.size,
                'install_type': app.installType,
                'install_commands': app.installCommands,
                'remove_commands': app.removeCommands,
                'check_installed': app.checkInstalled,
                if (app.checkVersion != null) 'check_version': app.checkVersion,
                if (app.updateCommands != null)
                  'update_commands': app.updateCommands,
                'tags': app.tags,
                'requires_reboot': app.requiresReboot,
                'pi_only': app.piOnly,
                if (app.dependencies.isNotEmpty)
                  'dependencies': app.dependencies,
              })
          .toList(),
    };

    return const JsonEncoder.withIndent('  ').convert(repositoryData);
  }

  Widget _buildGuiEditor() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Repository Information Section
          _buildRepositoryInfoSection(),
          const SizedBox(height: 24),

          // Categories Section
          _buildCategoriesSection(),
          const SizedBox(height: 24),

          // Applications Section
          _buildApplicationsSection(),
        ],
      ),
    );
  }

  Widget _buildRepositoryInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Repository Information',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        'File: $_currentFileName',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Name',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (_) => _markAsChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _versionController,
                    decoration: const InputDecoration(
                      labelText: 'Version',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (_) => _markAsChanged(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _markAsChanged(),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _sourceController,
                    decoration: const InputDecoration(
                      labelText: 'Source',
                      hintText: 'e.g., Official, Community',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (_) => _markAsChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _lastUpdatedController,
                    decoration: const InputDecoration(
                      labelText: 'Last Updated',
                      hintText: 'YYYY-MM-DD',
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Repository Types Section
            _buildRepositoryTypesSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildRepositoryTypesSelector() {
    final packageManagers = [
      {'id': 'apt', 'label': 'apt'},
      {'id': 'pacman', 'label': 'pacman'},
      {'id': 'rpm', 'label': 'rpm'},
    ];

    final universalTypes = [
      {'id': 'flatpak', 'label': 'flatpak'},
      {'id': 'snap', 'label': 'snap'},
      {'id': 'appimage', 'label': 'appimage'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Repository Types',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Select the package manager types this repository supports',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 12),
        // Package Managers (mutually exclusive)
        Text(
          'Package Managers (select one):',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: packageManagers.map((type) {
            final isSelected =
                _selectedRepositoryTypes.contains(type['id'] as String);
            return ElevatedButton(
              onPressed: () {
                setState(() {
                  if (isSelected) {
                    _selectedRepositoryTypes.remove(type['id'] as String);
                  } else {
                    // Remove other package managers
                    _selectedRepositoryTypes.removeWhere(
                        (t) => packageManagers.any((pm) => pm['id'] == t));
                    _selectedRepositoryTypes.add(type['id'] as String);
                  }
                  _markAsChanged();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                side: isSelected
                    ? BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      )
                    : BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                elevation: 0,
              ),
              child: Text(
                type['label'] as String,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        // Universal Types (can be combined)
        Text(
          'Universal Types (optional):',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: universalTypes.map((type) {
            final isSelected =
                _selectedRepositoryTypes.contains(type['id'] as String);
            return ElevatedButton(
              onPressed: () {
                setState(() {
                  if (isSelected) {
                    _selectedRepositoryTypes.remove(type['id'] as String);
                  } else {
                    _selectedRepositoryTypes.add(type['id'] as String);
                  }
                  _markAsChanged();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                side: isSelected
                    ? BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      )
                    : BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                elevation: 0,
              ),
              child: Text(
                type['label'] as String,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Categories (${_categories.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addCategory,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Category'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_categories.isEmpty)
              const Center(
                child: Text('No categories defined'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(Icons.category_outlined),
                      title: Text(category.name),
                      subtitle:
                          Text('${category.id} - ${category.description}'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () => _editCategory(index),
                            icon: const Icon(Icons.edit),
                          ),
                          IconButton(
                            onPressed: () => _deleteCategory(index),
                            icon: const Icon(Icons.delete),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildApplicationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.apps, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Applications (${_applications.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addApplication,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Application'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Search field
            TextField(
              controller: _applicationSearchController,
              decoration: const InputDecoration(
                labelText: 'Search Applications',
                hintText: 'Search by name, description, ID, or tags...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _filterApplications(),
            ),
            const SizedBox(height: 16),
            if (_filteredApplications.isEmpty &&
                _applicationSearchController.text.isNotEmpty)
              const Center(
                child: Text('No applications match your search'),
              )
            else if (_applications.isEmpty)
              const Center(
                child: Text('No applications defined'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredApplications.length,
                itemBuilder: (context, index) {
                  final app = _filteredApplications[index];
                  final originalIndex = _applications.indexOf(app);
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(Icons.apps),
                      title: Text(app.name),
                      subtitle: Text('${app.id} - ${app.description}'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () => _editApplication(originalIndex),
                            icon: const Icon(Icons.edit),
                          ),
                          IconButton(
                            onPressed: () => _deleteApplication(originalIndex),
                            icon: const Icon(Icons.delete),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Repository Editor'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.edit), text: 'Editor'),
            Tab(icon: Icon(Icons.code), text: 'Raw JSON'),
          ],
        ),
        actions: [
          // Manage repositories
          IconButton(
            onPressed: _showRepositoryManager,
            icon: const Icon(Icons.folder),
            tooltip: 'Repository Manager',
          ),
          if (_hasChanges)
            IconButton(
              onPressed: _isSaving ? null : _saveRepository,
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              tooltip: 'Save',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error: $_error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _showRepositoryManager,
                        child: const Text('Select Repository'),
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildGuiEditor(),
                    _buildRawJsonEditor(),
                  ],
                ),
    );
  }

  Widget _buildRawJsonEditor() {
    return Column(
      children: [
        // Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.code,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'repository.json',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          'Raw JSON repository editor',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  if (_hasChanges)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Modified',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),

        // Editor
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Consumer<AppState>(
              builder: (context, appState, child) => TextField(
                controller: _rawJsonController,
                maxLines: null,
                expands: true,
                style: TextStyle(
                  fontFamily: appState.terminalFontFamily,
                  fontSize: appState.terminalFontSize,
                ),
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Enter repository JSON...',
                  contentPadding: EdgeInsets.all(16),
                ),
                textAlignVertical: TextAlignVertical.top,
              ),
            ),
          ),
        ),

        // Bottom toolbar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              top: BorderSide(color: Theme.of(context).dividerColor),
            ),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Lines: $_lineCount • Characters: $_characterCount',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              const Spacer(),
              if (_hasChanges)
                FilledButton.icon(
                  onPressed: _isSaving ? null : _saveRepository,
                  icon: _isSaving
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.save, size: 16),
                  label: const Text('Save'),
                ),
            ],
          ),
        ),
      ],
    );
  }

  void _markAsChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }

    // Update raw JSON when GUI changes are made
    if (_tabController.index == 0 && _repository != null) {
      _rawJsonController.text = _buildJsonFromGui();
    }
  }

  Future<void> _loadSpecificRepositoryFile(File file) async {
    try {
      final content = await file.readAsString();

      // Validate JSON
      final jsonData = jsonDecode(content) as Map<String, dynamic>;
      final repository = AppHubRepository.fromJson(jsonData);

      // Update the editor with loaded data
      setState(() {
        _repository = repository;
        _currentFileName = file.path.split('/').last;
        _currentFilePath = file.path; // Store the full path
        _nameController.text = repository.repository.name;
        _versionController.text = repository.repository.version;
        _descriptionController.text = repository.repository.description;
        _sourceController.text = repository.repository.source;
        _lastUpdatedController.text =
            repository.repository.lastUpdated.toIso8601String().split('T')[0];
        _selectedRepositoryTypes = List.from(repository.repository.type);
        _categories = List.from(repository.categories);
        _categories.sort((a, b) => a.name.compareTo(b.name));

        _applications = List.from(repository.applications);
        _applications.sort((a, b) => a.name.compareTo(b.name));
        _filterApplications();
        _rawJsonController.text = content;
        _originalContent = content;
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Repository loaded from ${file.path.split('/').last}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _addCategory() {
    showDialog(
      context: context,
      builder: (context) => _CategoryDialog(
        title: 'Add Category',
        existingCategories: _categories,
        onSave: (category) {
          setState(() {
            _categories.add(category);
            _categories.sort((a, b) => a.name.compareTo(b.name));
            _markAsChanged();
          });
        },
      ),
    );
  }

  void _editCategory(int index) {
    showDialog(
      context: context,
      builder: (context) => _CategoryDialog(
        title: 'Edit Category',
        existingCategories: _categories,
        category: _categories[index],
        onSave: (category) {
          setState(() {
            _categories[index] = category;
            _categories.sort((a, b) => a.name.compareTo(b.name));
            _markAsChanged();
          });
        },
      ),
    );
  }

  void _deleteCategory(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
            'Are you sure you want to delete "${_categories[index].name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              setState(() {
                _categories.removeAt(index);
                _markAsChanged();
              });
              Navigator.of(context).pop();
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _addApplication() {
    showDialog(
      context: context,
      builder: (context) => _ApplicationDialog(
        title: 'Add Application',
        categories: _categories,
        repositoryTypes: _selectedRepositoryTypes,
        existingApplications: _applications,
        onSave: (application) {
          setState(() {
            _applications.add(application);
            _applications.sort((a, b) => a.name.compareTo(b.name));
            _filterApplications();
            _markAsChanged();
          });
        },
      ),
    );
  }

  void _editApplication(int index) {
    showDialog(
      context: context,
      builder: (context) => _ApplicationDialog(
        title: 'Edit Application',
        categories: _categories,
        repositoryTypes: _selectedRepositoryTypes,
        existingApplications: _applications,
        application: _applications[index],
        onSave: (application) {
          setState(() {
            _applications[index] = application;
            _applications.sort((a, b) => a.name.compareTo(b.name));
            _filterApplications();
            _markAsChanged();
          });
        },
      ),
    );
  }

  void _deleteApplication(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Application'),
        content: Text(
            'Are you sure you want to delete "${_applications[index].name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              setState(() {
                _applications.removeAt(index);
                _filterApplications();
                _markAsChanged();
              });
              Navigator.of(context).pop();
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _filterApplications() {
    final query = _applicationSearchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredApplications = List.from(_applications);
      } else {
        _filteredApplications = _applications.where((app) {
          return app.name.toLowerCase().contains(query) ||
              app.description.toLowerCase().contains(query) ||
              app.id.toLowerCase().contains(query) ||
              app.tags.any((tag) => tag.toLowerCase().contains(query));
        }).toList();
      }
    });
  }

  void _showRepositoryManager() async {
    // Get device OS from app state
    final appState = Provider.of<AppState>(context, listen: false);
    final deviceOS = appState.selectedDevice?.operatingSystem;

    final result = await Navigator.of(context).push<File?>(
      MaterialPageRoute(
        builder: (context) => _RepositoryManagerScreen(
          deviceOS: deviceOS,
          appHubService: widget.appHubService,
        ),
      ),
    );

    if (result != null) {
      await _loadSpecificRepositoryFile(result);
    } else {
      // User cancelled or didn't select a repository, go back
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }
}

// Category Dialog for adding/editing categories
class _CategoryDialog extends StatefulWidget {
  final String title;
  final AppCategory? category;
  final List<AppCategory> existingCategories;
  final Function(AppCategory) onSave;

  const _CategoryDialog({
    required this.title,
    required this.existingCategories,
    required this.onSave,
    this.category,
  });

  @override
  State<_CategoryDialog> createState() => _CategoryDialogState();
}

class _CategoryDialogState extends State<_CategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _idController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _iconController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _idController.text = widget.category!.id;
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description;
      _iconController.text = widget.category!.icon;
    }
  }

  @override
  void dispose() {
    _idController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _idController,
                decoration: const InputDecoration(
                  labelText: 'ID',
                  hintText: 'e.g., programming',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an ID';
                  }
                  if (!RegExp(r'^[a-z_]+$').hasMatch(value)) {
                    return 'ID must contain only lowercase letters and underscores';
                  }
                  // Check for duplicate IDs
                  if (_isDuplicateCategoryId(value)) {
                    return 'A category with this ID already exists';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  hintText: 'e.g., Programming',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText:
                      'e.g., IDEs, development tools, and programming languages',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _iconController,
                decoration: const InputDecoration(
                  labelText: 'Icon',
                  hintText: 'e.g., code (Material Icons name)',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an icon name';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveCategory,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveCategory() {
    if (_formKey.currentState!.validate()) {
      final category = AppCategory(
        id: _idController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        icon: _iconController.text.trim(),
      );
      widget.onSave(category);
      Navigator.of(context).pop();
    }
  }

  bool _isDuplicateCategoryId(String id) {
    // If editing an existing category, exclude it from the check
    final currentCategoryId = widget.category?.id;
    return widget.existingCategories
        .any((cat) => cat.id == id && cat.id != currentCategoryId);
  }
}

// Application Dialog for adding/editing applications
class _ApplicationDialog extends StatefulWidget {
  final String title;
  final AppHubApplication? application;
  final List<AppCategory> categories;
  final List<String> repositoryTypes;
  final List<AppHubApplication> existingApplications;
  final Function(AppHubApplication) onSave;

  const _ApplicationDialog({
    required this.title,
    required this.categories,
    required this.repositoryTypes,
    required this.existingApplications,
    required this.onSave,
    this.application,
  });

  @override
  State<_ApplicationDialog> createState() => _ApplicationDialogState();
}

class _ApplicationDialogState extends State<_ApplicationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _idController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _versionController = TextEditingController();
  final _iconController = TextEditingController();
  final _sizeController = TextEditingController();
  final _installCommandsController = TextEditingController();
  final _removeCommandsController = TextEditingController();
  final _checkInstalledController = TextEditingController();
  final _checkVersionController = TextEditingController();
  final _updateCommandsController = TextEditingController();
  final _tagsController = TextEditingController();
  final _dependenciesController = TextEditingController();

  String _selectedCategory = '';
  String _selectedInstallType = 'apt';
  bool _requiresReboot = false;
  bool _isGettingSize = false;
  List<String> _availableInstallTypes = [];

  @override
  void initState() {
    super.initState();

    // Build available install types based on repository types
    _availableInstallTypes = _buildAvailableInstallTypes();

    if (widget.application != null) {
      final app = widget.application!;
      _idController.text = app.id;
      _nameController.text = app.name;
      _descriptionController.text = app.description;
      _versionController.text = app.version;
      _selectedCategory = app.category;
      _iconController.text = app.icon;
      _sizeController.text = app.size;
      _selectedInstallType = app.installType;
      _installCommandsController.text = app.installCommands.join('\n');
      _removeCommandsController.text = app.removeCommands.join('\n');
      _checkInstalledController.text = app.checkInstalled;
      _checkVersionController.text = app.checkVersion ?? '';
      _updateCommandsController.text = app.updateCommands?.join('\n') ?? '';
      _tagsController.text = app.tags.join(', ');
      _requiresReboot = app.requiresReboot;
      _dependenciesController.text = app.dependencies.join(', ');
    } else {
      _selectedCategory =
          widget.categories.isNotEmpty ? widget.categories.first.id : '';
      _selectedInstallType = _availableInstallTypes.isNotEmpty
          ? _availableInstallTypes.first
          : 'script';
    }
  }

  List<String> _buildAvailableInstallTypes() {
    final types = <String>[];

    // Add package manager types based on repository type
    for (final repoType in widget.repositoryTypes) {
      switch (repoType.toLowerCase()) {
        case 'apt':
          if (!types.contains('apt')) types.add('apt');
          break;
        case 'pacman':
          if (!types.contains('pacman')) types.add('pacman');
          break;
        case 'rpm':
          if (!types.contains('yum')) types.add('yum');
          if (!types.contains('dnf')) types.add('dnf');
          break;
        case 'flatpak':
          if (!types.contains('flatpak')) types.add('flatpak');
          break;
        case 'snap':
          if (!types.contains('snap')) types.add('snap');
          break;
        case 'appimage':
          if (!types.contains('appimage')) types.add('appimage');
          break;
      }
    }

    // Always include these universal types
    if (!types.contains('script')) types.add('script');
    if (!types.contains('docker')) types.add('docker');

    return types;
  }

  @override
  void dispose() {
    _idController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _versionController.dispose();
    _iconController.dispose();
    _sizeController.dispose();
    _installCommandsController.dispose();
    _removeCommandsController.dispose();
    _checkInstalledController.dispose();
    _checkVersionController.dispose();
    _updateCommandsController.dispose();
    _tagsController.dispose();
    _dependenciesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: 800,
        height: 650,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Basic Information
                _buildSectionHeader('Basic Information'),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _idController,
                        decoration: const InputDecoration(
                          labelText: 'ID',
                          hintText: 'e.g., vscode',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an ID';
                          }
                          if (!RegExp(r'^[a-z0-9_-]+$').hasMatch(value)) {
                            return 'ID must contain only lowercase letters, numbers, hyphens, and underscores';
                          }
                          // Check for duplicate IDs
                          if (_isDuplicateId(value)) {
                            return 'An application with this ID already exists';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Name',
                          hintText: 'e.g., Visual Studio Code',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a name';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Brief description of the application',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _versionController,
                        decoration: const InputDecoration(
                          labelText: 'Version',
                          hintText: 'e.g., latest',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a version';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory.isNotEmpty &&
                                widget.categories
                                    .any((cat) => cat.id == _selectedCategory)
                            ? _selectedCategory
                            : null,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                        ),
                        items: widget.categories.map((category) {
                          return DropdownMenuItem(
                            value: category.id,
                            child: Text(category.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value ?? '';
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a category';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Installation Type
                _buildSectionHeader('Installation'),
                DropdownButtonFormField<String>(
                  value: _availableInstallTypes.contains(_selectedInstallType)
                      ? _selectedInstallType
                      : (_availableInstallTypes.isNotEmpty
                          ? _availableInstallTypes.first
                          : null),
                  decoration: const InputDecoration(
                    labelText: 'Install Type',
                    border: OutlineInputBorder(),
                  ),
                  items: _availableInstallTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.toUpperCase()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedInstallType = value ??
                          (_availableInstallTypes.isNotEmpty
                              ? _availableInstallTypes.first
                              : 'script');
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Display and Size
                _buildSectionHeader('Display and Size'),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _iconController,
                        decoration: InputDecoration(
                          labelText: 'Icon',
                          hintText: 'e.g., code (Material Icons name)',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            onPressed: _showIconPicker,
                            icon: const Icon(Icons.palette),
                            tooltip: 'Pick Icon',
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _sizeController,
                        decoration: InputDecoration(
                          labelText: 'Size',
                          hintText: 'e.g., ~200MB',
                          border: const OutlineInputBorder(),
                          suffixIcon: _canGetSize()
                              ? IconButton(
                                  onPressed:
                                      _isGettingSize ? null : _getPackageSize,
                                  icon: _isGettingSize
                                      ? const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                              strokeWidth: 2),
                                        )
                                      : const Icon(Icons.download),
                                  tooltip: 'Get Size',
                                )
                              : null,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Commands
                _buildSectionHeader('Commands'),
                TextFormField(
                  controller: _installCommandsController,
                  decoration: const InputDecoration(
                    labelText: 'Install Commands',
                    hintText: 'One command per line',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter install commands';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _removeCommandsController,
                  decoration: const InputDecoration(
                    labelText: 'Remove Commands',
                    hintText: 'One command per line',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter remove commands';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _checkInstalledController,
                  decoration: const InputDecoration(
                    labelText: 'Check Installed Command',
                    hintText: 'e.g., which vscode',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter check installed command';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Optional Commands
                _buildSectionHeader('Optional Commands'),
                TextFormField(
                  controller: _checkVersionController,
                  decoration: const InputDecoration(
                    labelText: 'Check Version Command (Optional)',
                    hintText: 'e.g., vscode --version',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _updateCommandsController,
                  decoration: const InputDecoration(
                    labelText: 'Update Commands (Optional)',
                    hintText: 'One command per line',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // Metadata
                _buildSectionHeader('Metadata'),
                TextFormField(
                  controller: _tagsController,
                  decoration: const InputDecoration(
                    labelText: 'Tags',
                    hintText: 'Comma-separated tags',
                    border: OutlineInputBorder(),
                  ),
                ),

                const SizedBox(height: 16),

                // Flags
                _buildSectionHeader('Options'),
                CheckboxListTile(
                  title: const Text('Requires Reboot'),
                  subtitle: const Text(
                      'Check if installation requires a system reboot'),
                  value: _requiresReboot,
                  onChanged: (value) {
                    setState(() {
                      _requiresReboot = value ?? false;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveApplication,
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Divider()),
        ],
      ),
    );
  }

  void _saveApplication() {
    if (_formKey.currentState!.validate()) {
      // This is a simplified save - we'll need to add more fields
      final application = AppHubApplication(
        id: _idController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        version: _versionController.text.trim(),
        category: _selectedCategory,
        icon: _iconController.text.trim().isNotEmpty
            ? _iconController.text.trim()
            : 'apps',
        size: _sizeController.text.trim().isNotEmpty
            ? _sizeController.text.trim()
            : '~1MB',
        installType: _selectedInstallType,
        installCommands: _installCommandsController.text.trim().isNotEmpty
            ? _installCommandsController.text.trim().split('\n')
            : ['echo "No install commands specified"'],
        removeCommands: _removeCommandsController.text.trim().isNotEmpty
            ? _removeCommandsController.text.trim().split('\n')
            : ['echo "No remove commands specified"'],
        checkInstalled: _checkInstalledController.text.trim().isNotEmpty
            ? _checkInstalledController.text.trim()
            : 'which ${_idController.text.trim()}',
        checkVersion: _checkVersionController.text.trim().isNotEmpty
            ? _checkVersionController.text.trim()
            : null,
        updateCommands: _updateCommandsController.text.trim().isNotEmpty
            ? _updateCommandsController.text.trim().split('\n')
            : null,
        tags: _tagsController.text.trim().isNotEmpty
            ? _tagsController.text
                .trim()
                .split(',')
                .map((e) => e.trim())
                .toList()
            : [],
        requiresReboot: _requiresReboot,
        piOnly: false, // Always false since this app is for RPi's only
        dependencies: [], // No dependencies needed for RPi-only apps
      );
      widget.onSave(application);
      Navigator.of(context).pop();
    }
  }

  bool _canGetSize() {
    return _selectedInstallType == 'apt' &&
        _idController.text.trim().isNotEmpty;
  }

  Future<void> _getPackageSize() async {
    if (!_canGetSize()) return;

    setState(() {
      _isGettingSize = true;
    });

    try {
      final packageName = _idController.text.trim();

      // Try multiple approaches to get package size
      String? sizeResult;

      // First try: apt-cache show
      try {
        final result = await Process.run('apt-cache', ['show', packageName]);
        if (result.exitCode == 0) {
          final output = result.stdout as String;
          final sizeMatch = RegExp(r'Size:\s*(\d+)').firstMatch(output);
          if (sizeMatch != null) {
            final sizeBytes = int.parse(sizeMatch.group(1)!);
            final sizeMB = (sizeBytes / (1024 * 1024)).toStringAsFixed(1);
            sizeResult = '~${sizeMB}MB';
          }
        }
      } catch (e) {
        // Continue to next method
      }

      // Second try: apt list with size info
      if (sizeResult == null) {
        try {
          final result =
              await Process.run('apt', ['list', '--installed', packageName]);
          if (result.exitCode == 0 && result.stdout.toString().isNotEmpty) {
            // Package is installed, try to get size from dpkg
            final dpkgResult = await Process.run(
                'dpkg-query', ['-Wf', '\${Installed-Size}', packageName]);
            if (dpkgResult.exitCode == 0) {
              final sizeKB = int.tryParse(dpkgResult.stdout.toString().trim());
              if (sizeKB != null) {
                final sizeMB = (sizeKB / 1024).toStringAsFixed(1);
                sizeResult = '~${sizeMB}MB';
              }
            }
          }
        } catch (e) {
          // Continue to fallback
        }
      }

      // Fallback: Use a reasonable default based on package type
      sizeResult ??= _getDefaultPackageSize(packageName);

      setState(() {
        _sizeController.text = sizeResult!;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Package size estimated: $sizeResult'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting package size: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isGettingSize = false;
      });
    }
  }

  String _getDefaultPackageSize(String packageName) {
    // Provide reasonable defaults based on common package patterns
    final name = packageName.toLowerCase();

    if (name.contains('lib') || name.contains('dev')) {
      return '~2MB';
    } else if (name.contains('editor') || name.contains('ide')) {
      return '~50MB';
    } else if (name.contains('browser') ||
        name.contains('firefox') ||
        name.contains('chrome')) {
      return '~100MB';
    } else if (name.contains('office') || name.contains('suite')) {
      return '~200MB';
    } else if (name.contains('game')) {
      return '~500MB';
    } else {
      return '~10MB';
    }
  }

  void _showIconPicker() {
    showDialog(
      context: context,
      builder: (context) => _IconPickerDialog(
        onIconSelected: (iconName) {
          setState(() {
            _iconController.text = iconName;
          });
        },
      ),
    );
  }

  bool _isDuplicateId(String id) {
    // If editing an existing application, exclude it from the check
    final currentAppId = widget.application?.id;
    return widget.existingApplications
        .any((app) => app.id == id && app.id != currentAppId);
  }
}

// Icon Picker Dialog for selecting Material Icons
class _IconPickerDialog extends StatefulWidget {
  final Function(String) onIconSelected;

  const _IconPickerDialog({
    required this.onIconSelected,
  });

  @override
  State<_IconPickerDialog> createState() => _IconPickerDialogState();
}

class _IconPickerDialogState extends State<_IconPickerDialog> {
  final _searchController = TextEditingController();
  List<String> _filteredIcons = [];

  // Common Material Icons for applications
  static const List<String> _commonIcons = [
    'apps',
    'code',
    'terminal',
    'web',
    'desktop_windows',
    'laptop',
    'phone_android',
    'tablet',
    'watch',
    'tv',
    'speaker',
    'headphones',
    'camera',
    'photo_camera',
    'videocam',
    'mic',
    'volume_up',
    'music_note',
    'play_arrow',
    'pause',
    'stop',
    'skip_next',
    'skip_previous',
    'shuffle',
    'repeat',
    'favorite',
    'star',
    'bookmark',
    'share',
    'download',
    'upload',
    'cloud',
    'cloud_download',
    'cloud_upload',
    'folder',
    'folder_open',
    'insert_drive_file',
    'description',
    'picture_as_pdf',
    'image',
    'movie',
    'library_music',
    'library_books',
    'book',
    'article',
    'newspaper',
    'rss_feed',
    'email',
    'message',
    'chat',
    'forum',
    'group',
    'person',
    'account_circle',
    'face',
    'mood',
    'public',
    'language',
    'translate',
    'location_on',
    'map',
    'directions',
    'local_shipping',
    'local_taxi',
    'flight',
    'hotel',
    'restaurant',
    'local_cafe',
    'local_bar',
    'shopping_cart',
    'store',
    'payment',
    'account_balance',
    'work',
    'business',
    'domain',
    'apartment',
    'home',
    'bed',
    'kitchen',
    'local_laundry_service',
    'fitness_center',
    'pool',
    'spa',
    'golf_course',
    'casino',
    'nightlife',
    'movie_creation',
    'palette',
    'brush',
    'format_paint',
    'color_lens',
    'gradient',
    'tune',
    'build',
    'settings',
    'construction',
    'handyman',
    'plumbing',
    'electrical_services',
    'agriculture',
    'eco',
    'park',
    'nature',
    'wb_sunny',
    'wb_cloudy',
    'ac_unit',
    'beach_access',
    'pool',
    'sports_esports',
    'sports_soccer',
    'sports_basketball',
    'sports_football',
    'sports_baseball',
    'sports_tennis',
    'sports_golf',
    'sports_hockey',
    'directions_bike',
    'directions_run',
    'directions_walk',
    'hiking',
    'kayaking',
    'kitesurfing',
    'paragliding',
    'sailing',
    'surfing',
    'snowboarding',
    'skiing_downhill',
    'sledding',
    'ice_skating',
    'roller_skating',
    'skateboarding',
    'snowmobile',
    'motorcycle',
    'directions_car',
    'local_gas_station',
    'car_repair',
    'car_rental',
    'traffic',
    'train',
    'subway',
    'tram',
    'bus_alert',
    'airport_shuttle',
    'school',
    'local_library',
    'menu_book',
    'quiz',
    'calculate',
    'science',
    'biotech',
    'psychology',
    'medical_services',
    'local_hospital',
    'local_pharmacy',
    'healing',
    'monitor_heart',
    'fitness_center',
    'self_improvement',
    'spa',
    'child_care',
    'elderly',
    'accessible',
    'security',
    'verified_user',
    'admin_panel_settings',
    'supervisor_account',
    'manage_accounts',
    'badge',
    'military_tech',
    'shield',
    'gavel',
    'balance',
    'policy',
    'privacy_tip',
    'lock',
    'lock_open',
    'key',
    'vpn_key',
    'password',
    'fingerprint',
    'face_unlock',
    'no_encryption',
    'enhanced_encryption',
    'https',
    'wifi',
    'wifi_off',
    'signal_wifi_4_bar',
    'network_wifi',
    'router',
    'device_hub',
    'cast',
    'cast_connected',
    'bluetooth',
    'bluetooth_connected',
    'bluetooth_disabled',
    'nfc',
    'usb',
    'cable',
    'power',
    'battery_full',
    'battery_charging_full',
    'solar_power',
    'flash_on',
    'highlight',
    'lightbulb',
    'wb_incandescent',
    'memory',
    'storage',
    'sd_storage',
    'usb',
    'sim_card',
    'smartphone',
    'tablet_mac',
    'laptop_mac',
    'computer',
    'keyboard',
    'mouse',
    'headset',
    'headset_mic',
    'speaker_group',
    'surround_sound',
    'equalizer',
    'graphic_eq',
    'piano',
    'guitar',
    'drums',
    'saxophone',
    'trumpet',
    'violin',
    'microphone',
    'radio',
    'podcasts',
    'hearing',
    'record_voice_over',
    'interpreter_mode',
    'subtitles',
    'closed_caption',
    'live_tv',
    'ondemand_video',
    'video_library',
    'movie_filter',
    'theaters',
    'local_movies',
    'confirmation_number',
    'event_seat',
    'weekend',
    'today',
    'event',
    'schedule',
    'access_time',
    'timer',
    'alarm',
    'alarm_on',
    'snooze',
    'timelapse',
    'slow_motion_video',
    'fast_forward',
    'fast_rewind',
    'speed',
    'trending_up',
    'trending_down',
    'show_chart',
    'analytics',
    'assessment',
    'bar_chart',
    'pie_chart',
    'donut_large',
    'timeline',
    'gantt_chart',
    'table_chart',
    'insert_chart',
    'functions',
    'data_usage',
    'poll',
    'leaderboard',
    'score'
  ];

  @override
  void initState() {
    super.initState();
    _filteredIcons = List.from(_commonIcons);
    _searchController.addListener(_filterIcons);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterIcons() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredIcons = List.from(_commonIcons);
      } else {
        _filteredIcons = _commonIcons
            .where((icon) => icon.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Icon'),
      content: SizedBox(
        width: 500,
        height: 600,
        child: Column(
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'Search Icons',
                hintText: 'Type to search...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  childAspectRatio: 1,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _filteredIcons.length,
                itemBuilder: (context, index) {
                  final iconName = _filteredIcons[index];
                  return InkWell(
                    onTap: () {
                      widget.onIconSelected(iconName);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(_getIconData(iconName), size: 24),
                          const SizedBox(height: 4),
                          Text(
                            iconName,
                            style: const TextStyle(fontSize: 8),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    // This is a simplified mapping - in a real app you'd want a more comprehensive mapping
    switch (iconName) {
      case 'apps':
        return Icons.apps;
      case 'code':
        return Icons.code;
      case 'terminal':
        return Icons.terminal;
      case 'web':
        return Icons.web;
      case 'desktop_windows':
        return Icons.desktop_windows;
      case 'laptop':
        return Icons.laptop;
      case 'phone_android':
        return Icons.phone_android;
      case 'tablet':
        return Icons.tablet;
      case 'camera':
        return Icons.camera;
      case 'photo_camera':
        return Icons.photo_camera;
      case 'videocam':
        return Icons.videocam;
      case 'music_note':
        return Icons.music_note;
      case 'play_arrow':
        return Icons.play_arrow;
      case 'folder':
        return Icons.folder;
      case 'settings':
        return Icons.settings;
      case 'build':
        return Icons.build;
      case 'download':
        return Icons.download;
      case 'upload':
        return Icons.upload;
      case 'cloud':
        return Icons.cloud;
      case 'wifi':
        return Icons.wifi;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'security':
        return Icons.security;
      case 'lock':
        return Icons.lock;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'share':
        return Icons.share;
      case 'email':
        return Icons.email;
      case 'message':
        return Icons.message;
      case 'person':
        return Icons.person;
      case 'group':
        return Icons.group;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'store':
        return Icons.store;
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'sports_esports':
        return Icons.sports_esports;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'palette':
        return Icons.palette;
      case 'brush':
        return Icons.brush;
      case 'tune':
        return Icons.tune;
      case 'memory':
        return Icons.memory;
      case 'storage':
        return Icons.storage;
      case 'computer':
        return Icons.computer;
      case 'smartphone':
        return Icons.smartphone;
      default:
        return Icons.apps; // Default fallback
    }
  }
}

// Repository Manager Screen for managing loaded repositories
class _RepositoryManagerScreen extends StatefulWidget {
  final String? deviceOS;
  final AppHubService? appHubService;

  const _RepositoryManagerScreen({this.deviceOS, this.appHubService});

  @override
  State<_RepositoryManagerScreen> createState() =>
      _RepositoryManagerScreenState();
}

class _RepositoryManagerScreenState extends State<_RepositoryManagerScreen> {
  List<File> _repositoryFiles = [];
  bool _isLoading = true;
  Map<String, String?> _defaultRepositories = {
    'apt': null,
    'pacman': null,
    'rpm': null,
  };

  @override
  void initState() {
    super.initState();
    _loadRepositoryFiles();
    _loadDefaultRepositories();
  }

  Future<void> _loadRepositoryFiles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final repoDir = await _getAppRepositoryDirectory();
      final files = repoDir
          .listSync()
          .where((entity) => entity is File && entity.path.endsWith('.json'))
          .cast<File>()
          .toList();

      setState(() {
        _repositoryFiles = files;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<Directory> _getAppRepositoryDirectory() async {
    final currentDir = Directory.current;
    String projectRoot = currentDir.path;
    if (projectRoot.contains('/build/')) {
      projectRoot = projectRoot.split('/build/').first;
    }
    final repoDir = Directory('$projectRoot/assets/repositories');

    if (!await repoDir.exists()) {
      await repoDir.create(recursive: true);
    }

    return repoDir;
  }

  Future<void> _loadDefaultRepositories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _defaultRepositories['apt'] = prefs.getString('default_repository_apt');
        _defaultRepositories['pacman'] =
            prefs.getString('default_repository_pacman');
        _defaultRepositories['rpm'] = prefs.getString('default_repository_rpm');
      });
    } catch (e) {
      debugPrint('Error loading default repositories: $e');
    }
  }

  Future<void> _setDefaultRepository(
      String packageManager, String? repositoryPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (repositoryPath != null) {
        await prefs.setString(
            'default_repository_$packageManager', repositoryPath);
      } else {
        await prefs.remove('default_repository_$packageManager');
      }

      setState(() {
        _defaultRepositories[packageManager] = repositoryPath;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(repositoryPath != null
                ? 'Set as default $packageManager repository'
                : 'Removed default $packageManager repository'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error setting default repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Repository Manager'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          // Create new repository
          IconButton(
            onPressed: _createNewRepository,
            icon: const Icon(Icons.add),
            tooltip: 'Create New Repository',
          ),
          // Import repository
          IconButton(
            onPressed: _importRepository,
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Repository',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _repositoryFiles.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.folder_open, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No repositories found'),
                      SizedBox(height: 8),
                      Text(
                        'Load a repository to get started',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _repositoryFiles.length,
                  itemBuilder: (context, index) {
                    final file = _repositoryFiles[index];
                    final fileName = file.path.split('/').last;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: FutureBuilder<Map<String, dynamic>?>(
                        future: _getRepositoryDetails(file),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const ListTile(
                              leading: Icon(Icons.description),
                              title: Text('Loading...'),
                              subtitle: Text('Reading repository information'),
                            );
                          }

                          final details = snapshot.data;
                          if (details == null) {
                            return ListTile(
                              leading:
                                  const Icon(Icons.error, color: Colors.red),
                              title: Text(fileName),
                              subtitle: const Text('Invalid repository file'),
                              trailing: IconButton(
                                onPressed: () => _deleteRepository(file, index),
                                icon: const Icon(Icons.delete),
                                tooltip: 'Delete',
                              ),
                            );
                          }

                          return InkWell(
                            onTap: () => _loadRepository(file),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(Icons.description),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    details['name'] ?? fileName,
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .titleMedium,
                                                  ),
                                                ),
                                                // Compatibility indicator
                                                if (widget.deviceOS !=
                                                    null) ...[
                                                  const SizedBox(width: 8),
                                                  _buildCompatibilityIndicator(
                                                      details['types'] as List<
                                                              String>? ??
                                                          []),
                                                ],
                                              ],
                                            ),
                                            Text(
                                              fileName,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurfaceVariant,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            onPressed: () =>
                                                _loadRepository(file),
                                            icon: const Icon(Icons.folder_open),
                                            tooltip: 'Load Repository',
                                          ),
                                          IconButton(
                                            onPressed: () =>
                                                _exportRepository(file),
                                            icon:
                                                const Icon(Icons.file_download),
                                            tooltip: 'Export Repository',
                                          ),
                                          IconButton(
                                            onPressed: () =>
                                                _deleteRepository(file, index),
                                            icon: const Icon(Icons.delete),
                                            tooltip: 'Delete',
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    details['description'] ?? 'No description',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surfaceContainerHighest,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          'v${details['version'] ?? '1.0.0'}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surfaceContainerHigh,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          '${details['appCount'] ?? 0} apps',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      if (details['types'] != null) ...[
                                        for (final type
                                            in details['types'] as List<String>)
                                          Container(
                                            margin:
                                                const EdgeInsets.only(right: 4),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .surfaceContainer,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .outline
                                                    .withValues(alpha: 0.3),
                                                width: 0.5,
                                              ),
                                            ),
                                            child: Text(
                                              _getTypeDisplayName(type),
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    fontSize: 10,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                            ),
                                          ),
                                      ],
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  // Default repository controls
                                  _buildDefaultRepositoryControls(
                                      file, details),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }

  Future<Map<String, dynamic>?> _getRepositoryDetails(File file) async {
    try {
      final content = await file.readAsString();
      final jsonData = jsonDecode(content) as Map<String, dynamic>;
      final repo = AppHubRepository.fromJson(jsonData);

      return {
        'name': repo.repository.name,
        'version': repo.repository.version,
        'description': repo.repository.description,
        'source': repo.repository.source,
        'types': repo.repository.type,
        'appCount': repo.applications.length,
        'categoryCount': repo.categories.length,
      };
    } catch (e) {
      return null;
    }
  }

  String _getTypeDisplayName(String type) {
    switch (type.toLowerCase()) {
      case 'apt':
        return 'APT';
      case 'pacman':
        return 'PACMAN';
      case 'rpm':
        return 'RPM';
      case 'flatpak':
        return 'FLATPAK';
      case 'snap':
        return 'SNAP';
      case 'appimage':
        return 'APPIMAGE';
      default:
        return type.toUpperCase();
    }
  }

  Widget _buildDefaultRepositoryControls(
      File file, Map<String, dynamic> details) {
    final types = details['types'] as List<String>? ?? [];
    final packageManagers = ['apt', 'pacman', 'rpm'];
    final supportedPackageManagers = types
        .where((type) => packageManagers.contains(type.toLowerCase()))
        .toList();

    if (supportedPackageManagers.isEmpty) {
      return const SizedBox.shrink();
    }

    // Check compatibility with device OS
    final isCompatible = _isRepositoryCompatible(types);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Default Repository Settings:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
            const SizedBox(width: 8),
            if (!isCompatible && widget.deviceOS != null) ...[
              Icon(
                Icons.warning,
                size: 16,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(width: 4),
              Text(
                'Incompatible with active device',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                      fontSize: 11,
                    ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: supportedPackageManagers.map((packageManager) {
            final isDefault =
                _defaultRepositories[packageManager.toLowerCase()] == file.path;

            return ElevatedButton.icon(
              onPressed: () {
                if (isDefault) {
                  _setDefaultRepository(packageManager.toLowerCase(), null);
                } else {
                  _setDefaultRepository(
                      packageManager.toLowerCase(), file.path);
                }
              },
              icon: Icon(
                isDefault ? Icons.star : Icons.star_border,
                size: 18,
              ),
              label: Text(
                isDefault
                    ? 'Default ${_getTypeDisplayName(packageManager)}'
                    : 'Set as ${_getTypeDisplayName(packageManager)} Default',
                style:
                    const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: isDefault
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: isDefault
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
                side: BorderSide(
                  color: isDefault
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.5),
                  width: 1,
                ),
                elevation: isDefault ? 2 : 0,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                minimumSize: const Size(120, 40),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  bool _isRepositoryCompatible(List<String> repositoryTypes) {
    if (widget.deviceOS == null) return true; // Can't determine compatibility

    final devicePackageManager = _getPackageManagerForOS(widget.deviceOS!);
    return repositoryTypes.any(
        (type) => type.toLowerCase() == devicePackageManager.toLowerCase());
  }

  String _getPackageManagerForOS(String os) {
    final osLower = os.toLowerCase();

    if (osLower.contains('ubuntu') ||
        osLower.contains('debian') ||
        osLower.contains('raspbian') ||
        osLower.contains('raspberry pi os')) {
      return 'apt';
    } else if (osLower.contains('arch') ||
        osLower.contains('manjaro') ||
        osLower.contains('endeavour')) {
      return 'pacman';
    } else if (osLower.contains('fedora') ||
        osLower.contains('centos') ||
        osLower.contains('rhel') ||
        osLower.contains('red hat')) {
      return 'rpm';
    }

    // Default to apt for unknown Linux distributions
    return 'apt';
  }

  Widget _buildCompatibilityIndicator(List<String> repositoryTypes) {
    final isCompatible = _isRepositoryCompatible(repositoryTypes);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isCompatible
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isCompatible ? Icons.check_circle : Icons.warning,
            size: 12,
            color: isCompatible
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onErrorContainer,
          ),
          const SizedBox(width: 4),
          Text(
            isCompatible ? 'Compatible' : 'Incompatible',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: isCompatible
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onErrorContainer,
                ),
          ),
        ],
      ),
    );
  }

  void _loadRepository(File file) async {
    try {
      // Load the repository directly into the App Hub service if available
      if (widget.appHubService != null) {
        await widget.appHubService!.loadRepositoryFromFile(file.path);
      }

      if (mounted) {
        final navigator = Navigator.of(context);
        final messenger = ScaffoldMessenger.of(context);

        messenger.showSnackBar(
          SnackBar(
            content: Text('Loaded repository: ${file.path.split('/').last}'),
            backgroundColor: Colors.green,
          ),
        );

        // Close the repository manager and return the file for editing if needed
        navigator.pop(file);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _createNewRepository() {
    showDialog(
      context: context,
      builder: (context) => _CreateRepositoryDialog(
        onRepositoryCreated: (file) {
          setState(() {
            _repositoryFiles.add(file);
          });
          Navigator.of(context).pop(file); // Return the new file
        },
      ),
    );
  }

  void _importRepository() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'Import Repository',
      );

      if (result != null && result.files.single.path != null) {
        final sourceFile = File(result.files.single.path!);
        final content = await sourceFile.readAsString();

        // Validate JSON
        final jsonData = jsonDecode(content) as Map<String, dynamic>;
        AppHubRepository.fromJson(jsonData); // Validate structure

        // Copy to app directory
        final appDir = await _getAppRepositoryDirectory();
        String fileName = result.files.single.name;
        if (!fileName.endsWith('.json')) {
          fileName = '$fileName.json';
        }

        final targetFile = File('${appDir.path}/$fileName');
        int counter = 1;
        while (await targetFile.exists()) {
          final nameWithoutExt = fileName.replaceAll('.json', '');
          fileName = '${nameWithoutExt}_$counter.json';
          counter++;
        }

        final finalFile = File('${appDir.path}/$fileName');
        await finalFile.writeAsString(content);

        setState(() {
          _repositoryFiles.add(finalFile);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Repository imported as $fileName'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error importing repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportRepository(File file) async {
    try {
      String? outputDir = await FilePicker.platform.getDirectoryPath(
        dialogTitle: 'Select Export Directory',
      );

      if (outputDir != null) {
        final fileName = file.path.split('/').last;
        final targetFile = File('$outputDir/$fileName');
        await file.copy(targetFile.path);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Exported $fileName'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteRepository(File file, int index) {
    final fileName = file.path.split('/').last;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Repository'),
        content: Text('Are you sure you want to delete "$fileName"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);

              try {
                await file.delete();
                if (mounted) {
                  setState(() {
                    _repositoryFiles.removeAt(index);
                  });
                  navigator.pop();
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Deleted $fileName'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  navigator.pop();
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Error deleting repository: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

// Create Repository Dialog
class _CreateRepositoryDialog extends StatefulWidget {
  final Function(File) onRepositoryCreated;

  const _CreateRepositoryDialog({
    required this.onRepositoryCreated,
  });

  @override
  State<_CreateRepositoryDialog> createState() =>
      _CreateRepositoryDialogState();
}

class _CreateRepositoryDialogState extends State<_CreateRepositoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _versionController = TextEditingController(text: '1.0.0');
  final _descriptionController = TextEditingController();
  final _sourceController = TextEditingController(text: 'Custom');
  final _fileNameController = TextEditingController();
  final List<String> _selectedTypes = ['apt'];

  @override
  void dispose() {
    _nameController.dispose();
    _versionController.dispose();
    _descriptionController.dispose();
    _sourceController.dispose();
    _fileNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Repository'),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Repository Name',
                    hintText: 'e.g., My Custom Repository',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a repository name';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    // Auto-generate filename
                    final fileName = value
                        .toLowerCase()
                        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
                        .replaceAll(RegExp(r'\s+'), '_');
                    _fileNameController.text = '${fileName}_repository.json';
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _versionController,
                        decoration: const InputDecoration(
                          labelText: 'Version',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a version';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _sourceController,
                        decoration: const InputDecoration(
                          labelText: 'Source',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a source';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Describe what this repository contains',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _fileNameController,
                  decoration: const InputDecoration(
                    labelText: 'File Name',
                    hintText: 'repository.json',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a file name';
                    }
                    if (!value.endsWith('.json')) {
                      return 'File name must end with .json';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Repository Types
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text('Repository Types:',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                ),
                const SizedBox(height: 8),
                _buildTypeSelector(),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createRepository,
          child: const Text('Create'),
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    final packageManagers = [
      {'id': 'apt', 'label': 'apt'},
      {'id': 'pacman', 'label': 'pacman'},
      {'id': 'rpm', 'label': 'rpm'},
    ];

    final universalTypes = [
      {'id': 'flatpak', 'label': 'flatpak'},
      {'id': 'snap', 'label': 'snap'},
      {'id': 'appimage', 'label': 'appimage'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Package Managers (mutually exclusive)
        Text(
          'Package Managers (select one):',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: packageManagers.map((type) {
            final isSelected = _selectedTypes.contains(type['id']);
            return ElevatedButton(
              onPressed: () {
                setState(() {
                  if (isSelected) {
                    _selectedTypes.remove(type['id']);
                  } else {
                    // Remove other package managers
                    _selectedTypes.removeWhere(
                        (t) => packageManagers.any((pm) => pm['id'] == t));
                    _selectedTypes.add(type['id']!);
                  }
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                side: isSelected
                    ? BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      )
                    : BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                elevation: 0,
              ),
              child: Text(
                type['label']!,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        // Universal Types (can be combined)
        Text(
          'Universal Types (optional):',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: universalTypes.map((type) {
            final isSelected = _selectedTypes.contains(type['id']);
            return ElevatedButton(
              onPressed: () {
                setState(() {
                  if (isSelected) {
                    _selectedTypes.remove(type['id']);
                  } else {
                    _selectedTypes.add(type['id']!);
                  }
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                side: isSelected
                    ? BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      )
                    : BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                elevation: 0,
              ),
              child: Text(
                type['label']!,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _createRepository() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one repository type'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // Create repository object
      final repository = AppHubRepository(
        repository: RepositoryInfo(
          name: _nameController.text,
          version: _versionController.text,
          description: _descriptionController.text,
          source: _sourceController.text,
          type: _selectedTypes,
          lastUpdated: DateTime.now(),
        ),
        categories: [],
        applications: [],
      );

      // Get app directory
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }
      final repoDir = Directory('$projectRoot/assets/repositories');
      if (!await repoDir.exists()) {
        await repoDir.create(recursive: true);
      }

      // Create file
      String fileName = _fileNameController.text;
      final targetFile = File('${repoDir.path}/$fileName');

      // Check if file exists and create unique name if needed
      int counter = 1;
      while (await targetFile.exists()) {
        final nameWithoutExt = fileName.replaceAll('.json', '');
        fileName = '${nameWithoutExt}_$counter.json';
        counter++;
      }

      final finalFile = File('${repoDir.path}/$fileName');
      final jsonContent =
          const JsonEncoder.withIndent('  ').convert(repository.toJson());
      await finalFile.writeAsString(jsonContent);

      widget.onRepositoryCreated(finalFile);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Repository created: $fileName'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating repository: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
